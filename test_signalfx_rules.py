#!/usr/bin/env python3
"""
Test script for SignalFx rules using the modified S3RuleEngine
"""

import json
from s3_rule_engine import S3RuleEngine


def test_comprehensive_scenarios():
    """Test various scenarios with SignalFx rules"""
    
    # Initialize rule engine with SignalFx rules
    engine = S3RuleEngine(rules_file_path="signalfx_rules.json")
    
    print("="*60)
    print("COMPREHENSIVE SIGNALFX RULES TESTING")
    print("="*60)
    
    # Test scenarios
    test_scenarios = [
        {
            "name": "RDS Free Storage Alert",
            "event": {
                "origin": "signalfx",
                "Type": "RDS_FreeStorage",
                "source": "Splunk Observability Cloud",
                "Severity": "Critical",
                "Resource": "RDS_FreeStorage"
            },
            "expected": "Yes"
        },
        {
            "name": "RDS CPU Utilization Alert", 
            "event": {
                "origin": "signalfx",
                "Type": "RDS_CPUUtilization",
                "source": "Splunk Observability Cloud",
                "Severity": "Warning"
            },
            "expected": "Yes"
        },
        {
            "name": "Clear Severity Event (should be filtered)",
            "event": {
                "origin": "signalfx",
                "Type": "RDS_CPUUtilization", 
                "source": "Splunk Observability Cloud",
                "Severity": "Clear"
            },
            "expected": "No"
        },
        {
            "name": "FSX Alert with Regex Match",
            "event": {
                "origin": "signalfx",
                "Type": "AWS_FSX_Performance_Alert",
                "source": "Splunk Observability Cloud", 
                "severity": "Major"
            },
            "expected": "Yes"
        },
        {
            "name": "ECS Memory Utilization",
            "event": {
                "origin": "signalfx",
                "Type": "CRBG_ ECS_Memory_Utilization",
                "source": "Splunk Observability Cloud",
                "Severity": "High"
            },
            "expected": "Yes"
        },
        {
            "name": "Azure Event",
            "event": {
                "origin": "signalfx",
                "source": "Splunk Observability Cloud",
                "messageBody": "Azure VM performance issue detected",
                "Severity": "Critical"
            },
            "expected": "Yes"
        },
        {
            "name": "Non-Splunk Source (should be filtered)",
            "event": {
                "origin": "signalfx",
                "Type": "RDS_FreeStorage",
                "source": "Other Monitoring Tool",
                "Severity": "Critical"
            },
            "expected": "No"
        },
        {
            "name": "Generic CPU Event",
            "event": {
                "origin": "signalfx",
                "source": "Splunk Observability Cloud",
                "Type": "Some_CPU_Alert",
                "Severity": "Warning"
            },
            "expected": "Yes"
        }
    ]
    
    # Run tests
    passed = 0
    total = len(test_scenarios)
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\nTest {i}: {scenario['name']}")
        print(f"Event: {json.dumps(scenario['event'], indent=2)}")
        
        result = engine.evaluate_incident_rules(scenario['event'])
        expected = scenario['expected']
        
        status = "✅ PASS" if result == expected else "❌ FAIL"
        if result == expected:
            passed += 1
            
        print(f"Expected: {expected}, Got: {result} - {status}")
        print("-" * 50)
    
    print(f"\nTest Summary: {passed}/{total} tests passed")
    print("="*60)


def test_custom_event():
    """Test with a custom event"""
    engine = S3RuleEngine(rules_file_path="signalfx_rules.json")
    
    print("\n" + "="*40)
    print("CUSTOM EVENT TEST")
    print("="*40)
    
    # You can modify this event to test specific scenarios
    custom_event = {
        "origin": "signalfx",
        "Type": "Your_Custom_Alert_Type",
        "source": "Splunk Observability Cloud",
        "Severity": "Critical",
        "messageBody": "Custom test message"
    }
    
    print(f"Custom Event: {json.dumps(custom_event, indent=2)}")
    result = engine.evaluate_incident_rules(custom_event)
    print(f"Result: {result}")


if __name__ == "__main__":
    test_comprehensive_scenarios()
    test_custom_event()
