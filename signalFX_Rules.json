[
  {
    "rule_id": "Splunk_RDS_FreeStorage",
    "description": "RDS Free Storage monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "Type", "operator": "contains", "value": "RDS_FreeStorage"},
      {"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"},
      {"field": "Severity", "operator": "not_contains", "value": "Clear"},
      {"field": "Resource", "operator": "contains", "value": "RDS_FreeStorage"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  },
  {
    "rule_id": "Splunk_RDS_CPU_Utilization",
    "description": "RDS CPU Utilization monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "Type", "operator": "contains", "value": "RDS_CPUUtilization"},
      {"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"},
      {"field": "Severity", "operator": "not_contains", "value": "Clear"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  },
  {
    "rule_id": "Splunk_RDS_Availability",
    "description": "RDS Availability monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "Type", "operator": "contains", "value": "RDS_Availability"},
      {"field": "Severity", "operator": "not_contains", "value": "Clear"},
      {"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  },
  {
    "rule_id": "SignalFX_PCS_ALB_NLB_UnHealthyHostCount",
    "description": "ALB/NLB Unhealthy Host Count monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"},
      {"field": "Type", "operator": "contains", "value": "UnHealthyHostCount"},
      {"field": "Severity", "operator": "not_contains", "value": "Clear"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  },
  {
    "rule_id": "SignalFX_SwapmemoryUtilization",
    "description": "Swap Memory Utilization monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "Type", "operator": "equals", "value": "PCS_CRBG_EC2_Prod_Linux_SwapmemoryUtilization"},
      {"field": "Severity", "operator": "not_contains", "value": "Clear"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  },
  {
    "rule_id": "SignalFX_CPU_Utilization",
    "description": "CPU Utilization monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  },
  {
    "rule_id": "SignalFX_Disk_Utilization",
    "description": "Disk Utilization monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  },
  {
    "rule_id": "SignalFX_AWS_backup_job_failure_events",
    "description": "AWS Backup Job Failure monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "Type", "operator": "equals", "value": "PCS_CRBG_BackupjobsFailed"},
      {"field": "Severity", "operator": "not_contains", "value": "Clear"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  },
  {
    "rule_id": "SignalFX_FSX_Alerts",
    "description": "FSX Alerts monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "source", "operator": "equals", "value": "Splunk Observability Cloud"},
      {"field": "Type", "operator": "matches_regex", "value": ".*[F|f][S|s][X|x].*"},
      {"field": "severity", "operator": "not_contains", "value": "Clear"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  },
  {
    "rule_id": "SignalFX_PCS_ECS_MemoryUTILIZATION",
    "description": "ECS Memory Utilization monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"},
      {"field": "Type", "operator": "equals", "value": "CRBG_ ECS_Memory_Utilization"},
      {"field": "Severity", "operator": "not_contains", "value": "Clear"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  },
  {
    "rule_id": "SignalFX_PaloAlto_EC2_CPUUtilization",
    "description": "PaloAlto EC2 CPU Utilization monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"},
      {"field": "Type", "operator": "contains", "value": "CRBG_PaloAlto_CPU_Utilization"},
      {"field": "Severity", "operator": "not_contains", "value": "Clear"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  },
  {
    "rule_id": "SignalFX_EC2_Status_Check",
    "description": "EC2 Status Check monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "source", "operator": "equals", "value": "Splunk Observability Cloud"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  },
  {
    "rule_id": "SignalFX_PCS_ECS_CPUUTILIZATION",
    "description": "ECS CPU Utilization monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "source", "operator": "equals", "value": "Splunk Observability Cloud"},
      {"field": "Type", "operator": "contains", "value": "CRBG_ECS_CPU_Utilization"},
      {"field": "Severity", "operator": "not_contains", "value": "Clear"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  },
  {
    "rule_id": "SignalFX_PCS_ECS_Docker_HeartBeat",
    "description": "ECS Docker HeartBeat monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"},
      {"field": "Type", "operator": "contains", "value": "CRBG_ECS_Docker"},
      {"field": "Severity", "operator": "not_contains", "value": "Clear"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  },
  {
    "rule_id": "SignalFX_Memory_Utilization",
    "description": "Memory Utilization monitoring rule",
    "priority": 1,
    "conditions": [
      {"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}
    ],
    "logic": "AND",
    "action": "Yes",
    "enabled": true
  }