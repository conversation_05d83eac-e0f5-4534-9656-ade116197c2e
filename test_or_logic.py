#!/usr/bin/env python3
"""
Test script specifically for testing OR logic in SignalFx rules
"""

from s3_rule_engine import S3RuleEng<PERSON>

def test_or_logic():
    # Initialize the rule engine with local JSON file
    engine = S3RuleEngine(rules_file_path="signalfx_rules.json")
    
    test_cases = [
        {
            "name": "CPU Utilization with sf_metric 'cpu' (lowercase)",
            "event": {
                "origin": "signalfx",
                "source": "Splunk Observability Cloud",
                "sf_metric": "cpu_utilization",
                "Severity": "Warning"
            },
            "expected": "Yes"
        },
        {
            "name": "CPU Utilization with sf_metric 'CPU' (uppercase)",
            "event": {
                "origin": "signalfx",
                "source": "Splunk Observability Cloud",
                "sf_metric": "CPU_utilization",
                "Severity": "Warning"
            },
            "expected": "Yes"
        },
        {
            "name": "Memory Utilization with sf_metric 'memory' (lowercase)",
            "event": {
                "origin": "signalfx",
                "source": "Splunk Observability Cloud",
                "sf_metric": "memory_usage",
                "Severity": "Warning"
            },
            "expected": "Yes"
        },
        {
            "name": "Memory Utilization with sf_metric 'Memory' (uppercase)",
            "event": {
                "origin": "signalfx",
                "source": "Splunk Observability Cloud",
                "sf_metric": "Memory_usage",
                "Severity": "Warning"
            },
            "expected": "Yes"
        },
        {
            "name": "Disk Utilization with sf_metric 'disk' (lowercase)",
            "event": {
                "origin": "signalfx",
                "source": "Splunk Observability Cloud",
                "sf_metric": "disk_usage",
                "Severity": "Warning"
            },
            "expected": "Yes"
        },
        {
            "name": "Disk Utilization with sf_metric 'Disk' (uppercase)",
            "event": {
                "origin": "signalfx",
                "source": "Splunk Observability Cloud",
                "sf_metric": "Disk_usage",
                "Severity": "Warning"
            },
            "expected": "Yes"
        },
        {
            "name": "EC2 Status Check - Prod Environment",
            "event": {
                "origin": "signalfx",
                "source": "Splunk Observability Cloud",
                "subResource": "CRBG_EC2_Prod_2/2_StatusCheck",
                "Severity": "Critical"
            },
            "expected": "Yes"
        },
        {
            "name": "EC2 Status Check - Non-Prod Environment",
            "event": {
                "origin": "signalfx",
                "source": "Splunk Observability Cloud",
                "subResource": "CRBG_EC2_Non_Prod_2/2_StatusCheck",
                "Severity": "Critical"
            },
            "expected": "Yes"
        },
        {
            "name": "CPU with Clear Severity (should be filtered)",
            "event": {
                "origin": "signalfx",
                "source": "Splunk Observability Cloud",
                "sf_metric": "cpu_utilization",
                "Severity": "Clear"
            },
            "expected": "No"
        },
        {
            "name": "CPU with ECS exclusion (should be filtered)",
            "event": {
                "origin": "signalfx",
                "source": "Splunk Observability Cloud",
                "sf_metric": "cpu_utilization",
                "subResource": "CRBG_ECS_CPU_Alert",
                "Severity": "Warning"
            },
            "expected": "No"
        }
    ]
    
    print("Testing OR Logic in SignalFx Rules...")
    print("=" * 60)
    
    passed = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        result = engine.evaluate_incident_rules(test_case["event"])
        status = "✅ PASS" if result == test_case["expected"] else "❌ FAIL"
        
        print(f"Test {i}: {test_case['name']}")
        print(f"Expected: {test_case['expected']}, Got: {result} - {status}")
        print("-" * 40)
        
        if result == test_case["expected"]:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    print(f"Success rate: {(passed/total)*100:.1f}%")

if __name__ == "__main__":
    test_or_logic()
