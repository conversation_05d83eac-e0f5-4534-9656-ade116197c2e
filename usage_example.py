#!/usr/bin/env python3
"""
Example usage of the modified S3RuleEngine with SignalFx rules
"""

from s3_rule_engine import S3RuleEngine

# Method 1: Test with local JSON file
def test_with_local_file():
    engine = S3RuleEngine(rules_file_path="signalfx_rules.json")
    
    # Your event data
    event = {
        "origin": "signalfx",
        "Type": "RDS_FreeStorage", 
        "source": "Splunk Observability Cloud",
        "Severity": "Critical",
        "Resource": "RDS_FreeStorage"
    }
    
    result = engine.evaluate_incident_rules(event)
    print(f"Should create incident: {result}")

# Method 2: Test with production S3 setup
def test_with_s3():
    import boto3
    
    engine = S3RuleEngine()
    engine.s3_client = boto3.client('s3')
    engine.bucket = "your-s3-bucket-name"
    
    # Your event data
    event = {"origin": "signalfx", "Type": "Some_Alert"}
    result = engine.evaluate_incident_rules(event)
    print(f"Should create incident: {result}")

if __name__ == "__main__":
    test_with_local_file()
