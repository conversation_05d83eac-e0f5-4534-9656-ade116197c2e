[{"rule_id": "network_filter_rule", "description": "Trigger when title matches any of the listed critical events", "priority": 1, "conditions": [{"logic": "OR", "conditions": [{"field": "title", "operator": "equals", "value": "ENVIRONMENTAL MONITOR SUPPLY STATUS CHANGE NOTIFICATION"}, {"field": "title", "operator": "equals", "value": "VLAN CREATED"}, {"field": "title", "operator": "equals", "value": "ISDN LAYER 2 CHANGE (MINOR)"}, {"field": "title", "operator": "equals", "value": "INTERFACE IS STALE"}, {"field": "title", "operator": "equals", "value": "SPANNING TREE ROOT HAS CHANGED"}, {"field": "title", "operator": "equals", "value": "ISDN LAYER 2 CHANGE (MAJOR)"}, {"field": "title", "operator": "equals", "value": "RADIUS ACCT SERVER UNAVAILABLE"}, {"field": "title", "operator": "equals", "value": "VPN CONNECTIVITY CHANGE"}, {"field": "title", "operator": "equals", "value": "UDLDP FAST HEL<PERSON><PERSON> STATUS CHANGE NOTIFICATION"}, {"field": "title", "operator": "equals", "value": "BSN AP IP ADDRESS FALLBACK"}, {"field": "title", "operator": "equals", "value": "FLASH DEVICE REMOVED"}, {"field": "title", "operator": "equals", "value": "FLASH DEVICE INSERTED"}, {"field": "title", "operator": "equals", "value": "POOR QUALITY OF VOICE NOTIFICATION"}, {"field": "title", "operator": "equals", "value": "MOBILITY ANCHOR DATA PATH DOWN"}, {"field": "title", "operator": "equals", "value": "RADIUS AUTH SERVER UNAVAILABLE"}, {"field": "title", "operator": "equals", "value": "MOBILITY ANCHOR CONTROL PATH DOWN"}, {"field": "title", "operator": "equals", "value": "AP POWER LOW"}, {"field": "title", "operator": "equals", "value": "DUPLICATE IP WITH DIFFERENT MAC DETECTED"}, {"field": "title", "operator": "equals", "value": "CRYPTOMAP DELETED"}, {"field": "title", "operator": "equals", "value": "PROVIDER CONDITION IS MINOR"}, {"field": "title", "operator": "equals", "value": "PROVIDER CONDITION IS MAJOR"}, {"field": "title", "operator": "equals", "value": "DCE OR NMM ATTACHMENT REPORTING CONTACT LOST"}, {"field": "title", "operator": "equals", "value": "ACCESS POINTS DISASSOCIATED FROM CONTROLLER EXCEEDED THRESHOLD"}, {"field": "title", "operator": "equals", "value": "GATEWAY TO REPEATER"}, {"field": "title", "operator": "equals", "value": "CISCO PROCESS RISING CPU UTILIZATION THRESHOLD"}, {"field": "title", "operator": "equals", "value": "RADIUS SERVER GL<PERSON>BAL DEACTIVATED"}, {"field": "title", "operator": "equals", "value": "CHASSIS MINOR ALARM"}, {"field": "title", "operator": "equals", "value": "ISDN DIALUP CALL FAILURE"}, {"field": "title", "operator": "equals", "value": "CHANGE IN AAA SERVER STATE DETECTED"}, {"field": "title", "operator": "equals", "value": "Inference handler has disasserted alarms on Network model"}, {"field": "title", "operator": "equals", "value": "CONTACT LOST TO NETWORK MODELS"}, {"field": "title", "operator": "equals", "value": "MODEL ACTIVATION TIME LIMIT EXCEEDED"}, {"field": "title", "operator": "equals", "value": "DUPLEX MISMATCH TRAP RECEIVED"}, {"field": "title", "operator": "equals", "value": "APPLICATION CONTACT LOST"}]}], "logic": "AND", "action": "No", "enabled": false}, {"rule_id": "uim_cdm_disk_free_windows", "description": "create incident only when the condition is met", "priority": 1, "conditions": [{"field": "title", "operator": "contains", "value": "disk free on"}, {"field": "event_detail", "operator": "contains", "value": "Probe ID: cdm"}, {"field": "title", "operator": "matches_regex", "value": ".+ on [A-Z]:.*\\S? is now .+"}, {"field": "spectrumseverity", "operator": "not_contains", "value": "CLEAR"}, {"field": "event_detail", "operator": "not_contains", "value": "cleared"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "Spectrum-CPU_Total_Usage", "description": "High CPU utilization creates incident", "priority": 1, "conditions": [{"field": "title", "operator": "contains", "value": "total cpu is"}, {"field": "event_detail", "operator": "contains", "value": "Probe ID: cdm"}, {"field": "spectrumseverity", "operator": "not_contains", "value": "CLEAR"}, {"field": "event_detail", "operator": "not_contains", "value": "cleared"}], "logic": "AND", "action": "Yes", "enabled": true}]