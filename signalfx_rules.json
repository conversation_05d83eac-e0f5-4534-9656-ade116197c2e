[{"rule_id": "Splunk_RDS_FreeStorage", "description": "Splunk - RDS FreeStorage", "priority": 1, "conditions": [{"field": "subResource", "operator": "contains", "value": "RDS_FreeStorage"}, {"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}, {"field": "Resource", "operator": "contains", "value": "RDS_FreeStorage"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "Splunk_RDS_CPU_Utilization", "description": "Splunk - RDS CPU Utilization", "priority": 1, "conditions": [{"field": "subResource", "operator": "contains", "value": "RDS_CPUUtilization"}, {"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "Splunk_RDS_Availability", "description": "Splunk - RDS Availability", "priority": 1, "conditions": [{"field": "subResource", "operator": "contains", "value": "RDS_Availability"}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}, {"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_PCS_ALB_NLB_UnHealthyHostCount", "description": "SignalFX - PCS_ALB\\NLB_UnHealthyHostCount", "priority": 1, "conditions": [{"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}, {"field": "subResource", "operator": "contains", "value": "UnHealthyHostCount"}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_SwapmemoryUtilization", "description": "SignalFX - SwapmemoryUtilization", "priority": 1, "conditions": [{"field": "subResource", "operator": "equals", "value": "PCS_CRBG_EC2_Prod_Linux_SwapmemoryUtilization"}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_CPU_Utilization", "description": "SignalFX - CPU - Utilization", "priority": 1, "conditions": [{"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}, {"logic": "OR", "conditions": [{"field": "sf_metric", "operator": "contains", "value": "cpu"}, {"field": "sf_metric", "operator": "contains", "value": "CPU"}]}, {"field": "subResource", "operator": "not_contains", "value": "PCS_HeartBeatCheck"}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}, {"field": "subResource", "operator": "not_contains", "value": "CRBG_PaloAlto_CPU_Utilization"}, {"field": "subResource", "operator": "not_contains", "value": "_ECS_"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_Disk_Utilization", "description": "SignalFX - Disk - Utilization", "priority": 1, "conditions": [{"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}, {"logic": "OR", "conditions": [{"field": "sf_metric", "operator": "contains", "value": "disk"}, {"field": "sf_metric", "operator": "contains", "value": "Disk"}]}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}, {"field": "subResource", "operator": "not_contains", "value": "FSX"}, {"field": "subResource", "operator": "not_contains", "value": "fsx"}, {"field": "subResource", "operator": "not_contains", "value": "FSx"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_AWS_backup_job_failure_events", "description": "SignalFX - AWS backup job failure events", "priority": 1, "conditions": [{"field": "subResource", "operator": "equals", "value": "PCS_CRBG_BackupjobsFailed"}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_FSX_Alerts", "description": "SignalFX - FSX Alerts", "priority": 1, "conditions": [{"field": "source", "operator": "equals", "value": "Splunk Observability Cloud"}, {"field": "subResource", "operator": "matches_regex", "value": ".*[F|f][S|s][X|x].*"}, {"field": "severity", "operator": "not_contains", "value": "Clear"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_PCS_ECS_MemoryUTILIZATION", "description": "SignalFX - PCS_ECS_MemoryUTILIZATION", "priority": 1, "conditions": [{"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}, {"field": "subResource", "operator": "equals", "value": "CRBG_ ECS_Memory_Utilization"}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_PaloAlto_EC2_CPUUtilization", "description": "SignalFX - PaloAlto_EC2_CPUUtilization", "priority": 1, "conditions": [{"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}, {"field": "subResource", "operator": "contains", "value": "CRBG_PaloAlto_CPU_Utilization"}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_EC2_Status_Check", "description": "SignalFX - EC2 - Status Check", "priority": 1, "conditions": [{"field": "source", "operator": "equals", "value": "Splunk Observability Cloud"}, {"logic": "OR", "conditions": [{"field": "subResource", "operator": "contains", "value": "CRBG_EC2_Prod_2/2_StatusCheck"}, {"field": "subResource", "operator": "contains", "value": "CRBG_EC2_Non_Prod_2/2_StatusCheck"}]}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_PCS_ECS_CPUUTILIZATION", "description": "SignalFX - PCS_ECS_CPUUTILIZATION", "priority": 1, "conditions": [{"field": "source", "operator": "equals", "value": "Splunk Observability Cloud"}, {"field": "subResource", "operator": "contains", "value": "CRBG_ECS_CPU_Utilization"}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_PCS_ECS_Docker_HeartBeat", "description": "SignalFX - PCS_ECS_Docker_HeartBeat", "priority": 1, "conditions": [{"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}, {"field": "subResource", "operator": "contains", "value": "CRBG_ECS_Docker"}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_Memory_Utilization", "description": "SignalFX - Memory - Utilization", "priority": 1, "conditions": [{"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}, {"logic": "OR", "conditions": [{"field": "sf_metric", "operator": "contains", "value": "memory"}, {"field": "sf_metric", "operator": "contains", "value": "Memory"}]}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_SwapmemoryUtilization_Duplicate", "description": "SignalFX - SwapmemoryUtilization", "priority": 1, "conditions": [{"field": "subResource", "operator": "equals", "value": "PCS_CRBG_EC2_Prod_Linux_SwapmemoryUtilization"}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_All_Events_Generic", "description": "SignalFX - All Events - Generic", "priority": 1, "conditions": [{"field": "source", "operator": "contains", "value": "Splunk Observability Cloud"}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}, {"field": "subResource", "operator": "contains", "value": "PCS_CRBG_EC2"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_Azure_Generic", "description": "SignalFX - Azure - Generic", "priority": 1, "conditions": [{"field": "source", "operator": "equals", "value": "Splunk Observability Cloud"}, {"field": "messageBody", "operator": "contains", "value": "azure"}, {"field": "Severity", "operator": "not_contains", "value": "Clear"}], "logic": "AND", "action": "Yes", "enabled": true}, {"rule_id": "SignalFX_Filter_All", "description": "Filter All SignalFx events", "priority": 1, "conditions": [{"field": "origin", "operator": "contains", "value": "signalfx"}], "logic": "AND", "action": "No", "enabled": true}]