#!/usr/bin/env python3
"""
Debug test to see which rules are being matched
"""

from s3_rule_engine import S3RuleE<PERSON><PERSON>

def debug_cpu_test():
    engine = S3RuleEngine(rules_file_path="signalfx_rules.json")
    
    # Test event that should match CPU rule
    event = {
        "origin": "signalfx",
        "source": "Splunk Observability Cloud",
        "sf_metric": "cpu_utilization",
        "Severity": "Warning"
    }
    
    print("Testing CPU event:")
    print(f"Event: {event}")
    print()
    
    # Get all rules and check each one
    rules = engine._get_rules("signalfx")
    
    for rule in rules:
        rule_id = rule.get('rule_id')
        conditions = rule.get('conditions', [])
        logic = rule.get('logic', 'AND')
        
        print(f"Checking rule: {rule_id}")
        
        # Evaluate this rule
        try:
            result = engine._evaluate_group(event, conditions, logic)
            print(f"  Result: {result}")
            
            if rule_id == "SignalFX_CPU_Utilization":
                print(f"  CPU Rule conditions: {conditions}")
                # Check each condition group
                for i, condition_group in enumerate(conditions):
                    if 'conditions' in condition_group:
                        group_result = engine._evaluate_group(event, condition_group['conditions'], condition_group['logic'])
                        print(f"    Group {i+1} ({condition_group['logic']}): {group_result}")
                        for j, cond in enumerate(condition_group['conditions']):
                            cond_result = engine._evaluate_condition(event, cond)
                            print(f"      Condition {j+1}: {cond} -> {cond_result}")
                    else:
                        cond_result = engine._evaluate_condition(event, condition_group)
                        print(f"    Direct condition: {condition_group} -> {cond_result}")
        except Exception as e:
            print(f"  Error: {e}")
        
        print()

if __name__ == "__main__":
    debug_cpu_test()
