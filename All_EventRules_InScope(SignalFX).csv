﻿Origin/Source,Event Rule Name,,,,,,
,,AND,,,,,
,Splunk - RDS FreeStorage,"Type contains ""RDS_FreeStorage""
source contains ""Splunk Observability Cloud""
Severity is not ""Clear""
Resource contains ""RDS_FreeStorage""",create ticket: yes,,,,
,,AND,,,,,
,"Splunk - RDS CPU Utilization
 ","Type contains ""RDS_CPUUtilization""
source contains ""Splunk Observability Cloud""
Severity is not ""Clear""",create ticket: yes,,,,
,,AND,,,,,
,Splunk - RDS Availability,"Type contains ""RDS_Availability""
Severity is not ""Clear""
source contains ""Splunk Observability Cloud""",create ticket: yes,,,,
,,AND,,,,,
,SignalFX - PCS_ALB\NLB_UnHealthyHostCount,"source contains ""Splunk Observability Cloud""
Type contains ""UnHealthyHostCount""
Severity is not ""Clear""",create ticket: yes,,,,
,,AND,,,,,
,SignalFX - SwapmemoryUtilization,"Type is ""PCS_CRBG_EC2_Prod_Linux_SwapmemoryUtilization""
Severity is not ""Clear""",create ticket: yes,,,,
,,,,OR,,AND,
,SignalFX - CPU - Utilization,"source contains ""Splunk Observability Cloud""",AND,"sf_metric contains ""cpu""
sf_metric contains ""CPU""",,"Type does not contain ""PCS_HeartBeatCheck""
Severity is not ""Clear""
Type does not contain ""CRBG_PaloAlto_CPU_Utilization""
Type does not contain ""_ECS_""",create ticket: yes
,,,,OR,,AND,
,SignalFX - Disk - Utilization,"source contains ""Splunk Observability Cloud""",AND,"sf_metric contains ""disk""
sf_metric contains ""Disk""",AND,"Severity is not ""Clear""
type does not contain ""FSX""
Type does not contain ""fsx""
Type does not contain ""FSx""",create ticket: yes
,,AND,,,,,
,SignalFX - AWS backup job failure events,"Type is ""PCS_CRBG_BackupjobsFailed""
Severity is not ""Clear""
 ",create ticket: yes,,,,
,,AND,,,,,
,SignalFX - FSX Alerts,"source is ""Splunk Observability Cloud""
Type matches regex "".*[F|f][S|s][X|x].*""
severity is not ""Clear""",create ticket: yes,,,,
,,AND,,,,,
,"SignalFX - PCS_ECS_MemoryUTILIZATION
 ","source contains ""Splunk Observability Cloud""
Type is ""CRBG_ ECS_Memory_Utilization""
Severity is not ""Clear""",create ticket: yes,,,,
,,AND,,,,,
,"SignalFX - PaloAlto_EC2_CPUUtilization
 ","source contains ""Splunk Observability Cloud""
Type contains ""CRBG_PaloAlto_CPU_Utilization""
Severity is not ""Clear""
 ",create ticket: yes,,,,
,,,,OR,,,
,"SignalFX - EC2 - Status Check
 ","source is ""Splunk Observability Cloud""",AND,"Type contains ""CRBG_EC2_Prod_2/2_StatusCheck""
Type contains ""CRBG_EC2_Non_Prod_2/2_StatusCheck""",AND,"Severity is not ""Clear""",create ticket: yes
,,AND,,,,,
,"SignalFX - PCS_ECS_CPUUTILIZATION
 ","source is ""Splunk Observability Cloud""
Type contains ""CRBG_ECS_CPU_Utilization""
Severity is not ""Clear""",,,,,
,,AND,,,,,
,SignalFX - PCS_ECS_Docker_HeartBeat,"source contains ""Splunk Observability Cloud""
Type contains ""CRBG_ECS_Docker""
Severity is not ""Clear""",create ticket: yes,,,,
,,,,OR,,,
,"SignalFX - Memory - Utilization
 ","source contains ""Splunk Observability Cloud""",AND,"sf_metric contains ""memory""
sf_metric contains ""Memory""",AND,"Severity is not ""Clear""",create ticket: yes
,,AND,,,,,
,"SignalFX - SwapmemoryUtilization
 ","Type is ""PCS_CRBG_EC2_Prod_Linux_SwapmemoryUtilization""
and
Severity is not ""Clear""",create ticket: yes,,,,
,,AND,,,,,
,SignalFX - All Events - Generic,"source contains ""Splunk Observability Cloud""
Severity is not ""Clear""
Type contains ""PCS_CRBG_EC2",create ticket: yes,,,,
,,AND,,,,,
,SignalFX - Azure - Generic,"source is ""Splunk Observability Cloud""
messageBody contains ""azure""
Severity is not ""Clear""",create ticket: yes,,,,
,,,,,,,
,SignalFX - Filter All,"source contains ""Splunk Observability Cloud""",create ticket: No,,,,
