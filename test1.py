from datetime import datetime, timedel<PERSON>

def generate_intervals_from_start(start_time_str, duration_minutes, duration_seconds):
    start_time = datetime.strptime(start_time_str, "%H:%M:%S")
    duration = timedelta(minutes=duration_minutes, seconds=duration_seconds)

    # FORWARD intervals
    forward_intervals = []
    current_start = start_time
    while current_start + duration <= datetime.strptime("23:59:59", "%H:%M:%S"):
        current_end = current_start + duration
        forward_intervals.append((current_start.time(), current_end.time()))
        current_start = current_end + timedelta(seconds=1)

    # BACKWARD intervals
    backward_intervals = []
    current_end = start_time - timedelta(seconds=1)
    while current_end - duration >= datetime.strptime("00:00:00", "%H:%M:%S"):
        current_start = current_end - duration
        backward_intervals.append((current_start.time(), current_end.time()))
        current_end = current_start - timedelta(seconds=1)

    # Combine and sort by time
    combined = backward_intervals[::-1] + forward_intervals
    return combined

# Example usage
start_time_input = "04:07:44"
duration_min = 14
duration_sec = 59

intervals = generate_intervals_from_start(start_time_input, duration_min, duration_sec)

# Print results
for i, (s, e) in enumerate(intervals):
    print(f"{i+1:02d}. Start: {s} | End: {e}")
