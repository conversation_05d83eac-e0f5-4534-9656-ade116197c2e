import json
import os

from typing import Dict, Any, List


class S3RuleEngine:
    def __init__(self):
            self.s3_client = boto3.client('s3')
            self.bucket = os.getenv('S3_BUCKET', 'alert360-dev-mvp-analytics-datalake-ap-southeast-2')
            self.rules_cache = {}
        
    def evaluate_incident_rules(self, event: Dict[str, Any]) -> str:
        """Returns 'Yes' or 'No' for incident creation"""
        monitoring_tool = event.get('origin', '').lower()
        rules = self._get_rules(monitoring_tool)
        
        for rule in sorted(rules, key=lambda x: x.get('priority', 999)):
            if self._evaluate_rule(event, rule):
                return rule.get('action', 'No')
        
        return 'No'
    
    def _get_rules(self, monitoring_tool: str) -> List[Dict[str, Any]]:
        """Get rules from S3"""
        cache_key = f"incident_rules_{monitoring_tool}"
        
        if cache_key in self.rules_cache:
            return self.rules_cache[cache_key]
        
        try:
            response = self.s3_client.get_object(
                Bucket=self.bucket,
                Key=f"IncidentRules/{monitoring_tool}_rules.json"
            )
            rules = json.loads(response['Body'].read().decode('utf-8'))
            self.rules_cache[cache_key] = rules
            return rules
        except:
            print(f"No rules found for {monitoring_tool}")
            return []
    
    def _evaluate_rule(self, event: Dict[str, Any], rule: Dict[str, Any]) -> bool:
        """Evaluate rule conditions"""
        conditions = rule.get('conditions', [])
        logic = rule.get('logic', 'AND')
        
        if not conditions:
            return False
            
        return self._evaluate_group(event, conditions, logic)
    
    def _evaluate_group(self, event: Dict[str, Any], conditions: List[Dict], logic: str) -> bool:
        """Evaluate condition group with AND/OR logic"""
        results = []
        
        for condition in conditions:
            if 'conditions' in condition:
                result = self._evaluate_group(event, condition['conditions'], condition['logic'])
            else:
                result = self._evaluate_condition(event, condition)
            results.append(result)
        
        return all(results) if logic == 'AND' else any(results)
    
    def _evaluate_condition(self, event: Dict[str, Any], condition: Dict[str, Any]) -> bool:
        """Evaluate single condition"""
        field = condition.get('field')
        operator = condition.get('operator')
        value = condition.get('value')
        
        if not all([field, operator, value is not None]):
            return False
        
        field_value = self._get_field_value(event, field)
        if field_value is None:
            return False
        
        field_str = str(field_value).lower()
        value_str = str(value).lower()
        
        if operator == 'contains':
            return value_str in field_str
        elif operator == 'not_contains':
            return value_str not in field_str
        elif operator == 'equals':
            return field_str == value_str
        elif operator == 'starts_with':
            return field_str.startswith(value_str)
        elif operator == 'ends_with':
            return field_str.endswith(value_str)
        
        return False
    
    def _get_field_value(self, event: Dict[str, Any], field_path: str) -> Any:
        """Get nested field value"""
        try:
            value = event
            for key in field_path.split('.'):
                value = value.get(key) if isinstance(value, dict) else None
            return value
        except:
            return None