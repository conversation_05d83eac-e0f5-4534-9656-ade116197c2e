import json
import os
import re

from typing import Dict, Any, List


class S3RuleEngine:
    def __init__(self, rules_file_path: str = None):
        print("Initializing S3RuleEngine")
        self.rules_cache = {}
        self.rules_file_path = rules_file_path

        # Initialize S3 client and bucket for production use
        self.s3_client = None
        self.bucket = None
        
    def evaluate_incident_rules(self, event: Dict[str, Any]) -> str:
        """Returns 'Yes' or 'No' for incident creation"""
        monitoring_tool = event.get('origin', '').lower()
        rules = self._get_rules(monitoring_tool)
        
        for rule in sorted(rules, key=lambda x: x.get('priority', 999)):
            if self._evaluate_rule(event, rule):
                return rule.get('action', 'No')
        
        return 'No'
    
    def _get_rules(self, monitoring_tool: str) -> List[Dict[str, Any]]:
        """Get rules from local file or S3"""
        cache_key = f"incident_rules_{monitoring_tool}"

        if cache_key in self.rules_cache:
            return self.rules_cache[cache_key]

        # Try to load from local file first (for testing)
        if self.rules_file_path and os.path.exists(self.rules_file_path):
            try:
                with open(self.rules_file_path, 'r') as f:
                    rules = json.load(f)
                self.rules_cache[cache_key] = rules
                print(f"Loaded {len(rules)} rules from local file: {self.rules_file_path}")
                return rules
            except Exception as e:
                print(f"Error loading local rules file: {e}")

        # Fallback to S3 (for production)
        if self.s3_client and self.bucket:
            try:
                response = self.s3_client.get_object(
                    Bucket=self.bucket,
                    Key=f"IncidentRules/{monitoring_tool}_rules.json"
                )
                rules = json.loads(response['Body'].read().decode('utf-8'))
                self.rules_cache[cache_key] = rules
                print(f"Loaded {len(rules)} rules from S3")
                return rules
            except Exception as e:
                print(f"Error loading rules from S3: {e}")

        print(f"No rules found for {monitoring_tool}")
        return []
    
    def _evaluate_rule(self, event: Dict[str, Any], rule: Dict[str, Any]) -> bool:
        """Evaluate rule conditions"""
        conditions = rule.get('conditions', [])
        logic = rule.get('logic', 'AND')
        
        if not conditions:
            return False
            
        return self._evaluate_group(event, conditions, logic)
    
    def _evaluate_group(self, event: Dict[str, Any], conditions: List[Dict], logic: str) -> bool:
        """Evaluate condition group with AND/OR logic"""
        results = []
        
        for condition in conditions:
            if 'conditions' in condition:
                result = self._evaluate_group(event, condition['conditions'], condition['logic'])
            else:
                result = self._evaluate_condition(event, condition)
            results.append(result)
        
        return all(results) if logic == 'AND' else any(results)
    
    def _evaluate_condition(self, event: Dict[str, Any], condition: Dict[str, Any]) -> bool:
        """Evaluate single condition"""
        field = condition.get('field')
        operator = condition.get('operator')
        value = condition.get('value')
        
        if not all([field, operator, value is not None]):
            return False
        
        field_value = self._get_field_value(event, field)
        if field_value is None:
            return False
        
        field_str = str(field_value).lower()
        value_str = str(value).lower()
        
        if operator == 'contains':
            return value_str in field_str
        elif operator == 'not_contains':
            return value_str not in field_str
        elif operator == 'equals':
            return field_str == value_str
        elif operator == 'starts_with':
            return field_str.startswith(value_str)
        elif operator == 'ends_with':
            return field_str.endswith(value_str)
        elif operator == 'matches_regex':
            try:
                return bool(re.search(str(value), str(field_value), re.IGNORECASE))
            except Exception as e:
                print(f"Regex error: {e}")
                return False

        return False
    
    def _get_field_value(self, event: Dict[str, Any], field_path: str) -> Any:
        """Get nested field value"""
        try:
            value = event
            for key in field_path.split('.'):
                value = value.get(key) if isinstance(value, dict) else None
            return value
        except:
            return None

    def test_rules(self, test_events: List[Dict[str, Any]]) -> None:
        """Test rules against sample events"""
        print("\n" + "="*50)
        print("TESTING RULE ENGINE")
        print("="*50)

        for i, event in enumerate(test_events, 1):
            print(f"\nTest Event {i}:")
            print(f"Event: {json.dumps(event, indent=2)}")

            result = self.evaluate_incident_rules(event)
            print(f"Result: {result}")
            print("-" * 30)


def main():
    """Test the rule engine with SignalFx rules"""

    # Initialize rule engine with local JSON file
    engine = S3RuleEngine(rules_file_path="signalfx_rules.json")

    # Sample test events for SignalFx
    test_events = [
        {
            "origin": "signalfx",
            "Type": "RDS_FreeStorage",
            "source": "Splunk Observability Cloud",
            "Severity": "Critical",
            "Resource": "RDS_FreeStorage"
        },
        {
            "origin": "signalfx",
            "Type": "PCS_CRBG_EC2_Prod_Linux_SwapmemoryUtilization",
            "source": "Splunk Observability Cloud",
            "Severity": "Warning"
        },
        {
            "origin": "signalfx",
            "Type": "UnHealthyHostCount",
            "source": "Splunk Observability Cloud",
            "Severity": "Clear"
        },
        {
            "origin": "signalfx",
            "Type": "FSX_Alert_Test",
            "source": "Splunk Observability Cloud",
            "severity": "Major"
        },
        {
            "origin": "signalfx",
            "Type": "Random_Event",
            "source": "Other Source",
            "Severity": "Critical"
        }
    ]

    # Test the rules
    engine.test_rules(test_events)


if __name__ == "__main__":
    main()