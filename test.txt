
Splunk_RDS_FreeStorage
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
Type contains "RDS_FreeStorage"
source contains "Splunk Observability Cloud"
Severity is not "Clear"
Resource contains "RDS_FreeStorage"
------------------

Splunk_RDS_CPU_Utilization
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"Type contains ""RDS_CPUUtilization""
source contains ""Splunk Observability Cloud""
Severity is not ""Clear"""
------------------

Splunk_RDS_Availability
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"Type contains ""RDS_Availability""
Severity is not ""Clear""
source contains ""Splunk Observability Cloud"""
------------------

SignalFX_PCS_ALB_NLB_UnHealthyHostCount
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"source contains ""Splunk Observability Cloud""
Type contains ""UnHealthyHostCount""
Severity is not ""Clear"""
------------------

SignalFX_SwapmemoryUtilization
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"Type is ""PCS_CRBG_EC2_Prod_Linux_SwapmemoryUtilization""
Severity is not ""Clear"""
------------------

SignalFX_CPU_Utilization
action: "Yes"
enabled: "True"
#logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
------------------

SignalFX_Disk_Utilization
action: "Yes"
enabled: "True"
#logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
------------------

SignalFX_AWS_backup_job_failure_events
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"Type is "PCS_CRBG_BackupjobsFailed"
Severity is not "Clear"
------------------

SignalFX_FSX_Alerts
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source is ""Splunk Observability Cloud"
Type matches regex ".*[F|f][S|s][X|x].*"
severity is not ""Clear"
------------------

SignalFX_PCS_ECS_MemoryUTILIZATION
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
Type is "CRBG_ ECS_Memory_Utilization"
Severity is not "Clear"
------------------

SignalFX_PaloAlto_EC2_CPUUtilization
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
Type contains "CRBG_PaloAlto_CPU_Utilization"
Severity is not "Clear"
------------------

SignalFX_EC2_Status_Check
action: "Yes"
enabled: "True"
#logic: "AND"
--------Fields----------
source is "Splunk Observability Cloud"
------------------

SignalFX_PCS_ECS_CPUUTILIZATION
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source is "Splunk Observability Cloud"
Type contains "CRBG_ECS_CPU_Utilization"
Severity is not "Clear"
------------------

SignalFX_PCS_ECS_Docker_HeartBeat
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
Type contains "CRBG_ECS_Docker"
Severity is not "Clear"
------------------

SignalFX_Memory_Utilization
action: "Yes"
enabled: "True"
#logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
------------------

SignalFX_SwapmemoryUtilization
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
Type is "PCS_CRBG_EC2_Prod_Linux_SwapmemoryUtilization"
Severity is not "Clear"
------------------

SignalFX_All_Events_Generic
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
Severity is not "Clear"
Type contains ""PCS_CRBG_EC2"
------------------

SignalFX_Azure_Generic
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source is "Splunk Observability Cloud"
messageBody contains "azure"
Severity is not "Clear"
------------------

SignalFX_Filter_All
action: "No"
enabled: "True"
#logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
------------------

Spectrum_Network_Filter_Rule
action: "No"
enabled: "False"
logic: "AND"
--------Fields----------
title is "ENVIRONMENTAL MONITOR SUPPLY STATUS CHANGE NOTIFICATION"
title is "VLAN CREATED"
title is "ISDN LAYER 2 CHANGE (MINOR)"
title is "INTERFACE IS STALE"
title is "SPANNING TREE ROOT HAS CHANGED"
title is "ISDN LAYER 2 CHANGE (MAJOR)"
title is "RADIUS ACCT SERVER UNAVAILABLE"
title is "VPN CONNECTIVITY CHANGE"
title is "UDLDP FAST HELLO STATUS CHANGE NOTIFICATION"
title is "BSN AP IP ADDRESS FALLBACK"
title is "FLASH DEVICE REMOVED"
title is "FLASH DEVICE INSERTED"
title is "POOR QUALITY OF VOICE NOTIFICATION"
title is "MOBILITY ANCHOR DATA PATH DOWN"
title is "RADIUS AUTH SERVER UNAVAILABLE"
title is "MOBILITY ANCHOR CONTROL PATH DOWN"
title is "AP POWER LOW"
title is "DUPLICATE IP WITH DIFFERENT MAC DETECTED"
title is "CRYPTOMAP DELETED"
title is "PROVIDER CONDITION IS MINOR"
title is "PROVIDER CONDITION IS MAJOR"
title is "DCE OR NMM ATTACHMENT REPORTING CONTACT LOST"
title is "ACCESS POINTS DISASSOCIATED FROM CONTROLLER EXCEEDED THRESHOLD"
title is "GATEWAY TO REPEATER"
title is "CISCO PROCESS RISING CPU UTILIZATION THRESHOLD"
title is "RADIUS SERVER GLOBAL DEACTIVATED"
title is "CHASSIS MINOR ALARM"
title is "ISDN DIALUP CALL FAILURE"
title is "CHANGE IN AAA SERVER STATE DETECTED"
title is "Inference handler has disasserted alarms on Network model"
title is "CONTACT LOST TO NETWORK MODELS"
title is "MODEL ACTIVATION TIME LIMIT EXCEEDED"
title is "DUPLEX MISMATCH TRAP RECEIVED"
title is "APPLICATION CONTACT LOST"
------------------

Spectrum_UIM_CDM_Disk_Free_Windows
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
title contains "disk free on"
event_detail contains "Probe ID: cdm"
title matches regex ".+ on [A-Z]:.*\\S? is now .+"
spectrumseverity is not "CLEAR"
event_detail is not "cleared"
------------------

Spectrum_CPU_Total_Usage
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
title contains "total cpu is"
event_detail contains "Probe ID: cdm"
spectrumseverity is not "CLEAR"
event_detail is not "cleared"
------------------
