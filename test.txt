
Splunk_RDS_FreeStorage
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
Type contains "RDS_FreeStorage"
source contains "Splunk Observability Cloud"
Severity is not "Clear"
Resource contains "RDS_FreeStorage"
------------------

Splunk_RDS_CPU_Utilization
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"Type contains ""RDS_CPUUtilization""
source contains ""Splunk Observability Cloud""
Severity is not ""Clear"""
------------------

Splunk_RDS_Availability
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"Type contains ""RDS_Availability""
Severity is not ""Clear""
source contains ""Splunk Observability Cloud"""
------------------

SignalFX_PCS_ALB_NLB_UnHealthyHostCount
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"source contains ""Splunk Observability Cloud""
Type contains ""UnHealthyHostCount""
Severity is not ""Clear"""
------------------

SignalFX_SwapmemoryUtilization
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"Type is ""PCS_CRBG_EC2_Prod_Linux_SwapmemoryUtilization""
Severity is not ""Clear"""
------------------

SignalFX_CPU_Utilization
action: "Yes"
enabled: "True"
logic between 3 sets: "AND"
logic set1: "AND"
logic set2: "OR"
logic set3: "AND"
--------Fields set 1----------
source contains "Splunk Observability Cloud"
------------------
--------Fields set 2----------
"sf_metric contains ""cpu""
sf_metric contains ""CPU"""
-------------------------
--------Fields set 3----------
"Type does not contain "PCS_HeartBeatCheck"
Severity is not "Clear"
Type does not contain "CRBG_PaloAlto_CPU_Utilization"
Type does not contain "_ECS_"
-------------------------


SignalFX_Disk_Utilization
action: "Yes"
enabled: "True"
logic set1: "AND"
logic set2: "OR"
logic set3: "AND"
logic between 3 sets: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
------------------
--------Fields set 2----------
sf_metric contains "disk"
sf_metric contains "Disk"
-------------------------
--------Fields set 3----------
Severity is not "Clear"
type does not contain "FSX"
Type does not contain "fsx"
Type does not contain "FSx"
-------------------------

SignalFX_AWS_backup_job_failure_events
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"Type is "PCS_CRBG_BackupjobsFailed"
Severity is not "Clear"
------------------

SignalFX_FSX_Alerts
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source is ""Splunk Observability Cloud"
Type matches regex ".*[F|f][S|s][X|x].*"
severity is not ""Clear"
------------------

SignalFX_PCS_ECS_MemoryUTILIZATION
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
Type is "CRBG_ ECS_Memory_Utilization"
Severity is not "Clear"
------------------

SignalFX_PaloAlto_EC2_CPUUtilization
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
Type contains "CRBG_PaloAlto_CPU_Utilization"
Severity is not "Clear"
------------------

SignalFX_EC2_Status_Check
action: "Yes"
enabled: "True"
logic set1: "AND"
logic set2: "OR"
logic set3: "AND"
logic between 3 sets: "AND"
--------Fields set1----------
source is "Splunk Observability Cloud"
------------------
--------Fields set2----------
Type contains "CRBG_EC2_Prod_2/2_StatusCheck"
Type contains "CRBG_EC2_Non_Prod_2/2_StatusCheck"
------------------
--------Fields set3----------
Severity is not "Clear"
------------------


SignalFX_PCS_ECS_CPUUTILIZATION
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source is "Splunk Observability Cloud"
Type contains "CRBG_ECS_CPU_Utilization"
Severity is not "Clear"
------------------

SignalFX_PCS_ECS_Docker_HeartBeat
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
Type contains "CRBG_ECS_Docker"
Severity is not "Clear"
------------------

SignalFX_Memory_Utilization
action: "Yes"
enabled: "True"
logic set1: "AND"
logic set2: "OR"
logic set3: "AND"
logic between 3 sets: "AND"
--------Fields set1----------
source contains "Splunk Observability Cloud"
------------------
--------Fields set2----------
sf_metric contains "memory"
sf_metric contains "Memory"
------------------
--------Fields set3----------
Severity is not "Clear"
------------------

SignalFX_SwapmemoryUtilization
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
Type is "PCS_CRBG_EC2_Prod_Linux_SwapmemoryUtilization"
Severity is not "Clear"
------------------

SignalFX_All_Events_Generic
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
Severity is not "Clear"
Type contains ""PCS_CRBG_EC2"
------------------

SignalFX_Azure_Generic
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source is "Splunk Observability Cloud"
messageBody contains "azure"
Severity is not "Clear"
------------------

SignalFX_Filter_All
action: "No"
enabled: "True"
#logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
------------------