
Splunk_RDS_FreeStorage
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
Type contains "RDS_FreeStorage"
source contains "Splunk Observability Cloud"
Severity is not "Clear"
Resource contains "RDS_FreeStorage"
------------------

Splunk_RDS_CPU_Utilization
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"Type contains ""RDS_CPUUtilization""
source contains ""Splunk Observability Cloud""
Severity is not ""Clear"""
------------------

Splunk_RDS_Availability
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"Type contains ""RDS_Availability""
Severity is not ""Clear""
source contains ""Splunk Observability Cloud"""
------------------

SignalFX_PCS_ALB_NLB_UnHealthyHostCount
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"source contains ""Splunk Observability Cloud""
Type contains ""UnHealthyHostCount""
Severity is not ""Clear"""
------------------

SignalFX_SwapmemoryUtilization
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"Type is ""PCS_CRBG_EC2_Prod_Linux_SwapmemoryUtilization""
Severity is not ""Clear"""
------------------

SignalFX_CPU_Utilization
action: "Yes"
enabled: "True"
#logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
------------------

SignalFX_Disk_Utilization
action: "Yes"
enabled: "True"
#logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
------------------

SignalFX_AWS_backup_job_failure_events
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
"Type is "PCS_CRBG_BackupjobsFailed"
Severity is not "Clear"
------------------

SignalFX_FSX_Alerts
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source is ""Splunk Observability Cloud"
Type matches regex ".*[F|f][S|s][X|x].*"
severity is not ""Clear"
------------------

SignalFX_PCS_ECS_MemoryUTILIZATION
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
Type is "CRBG_ ECS_Memory_Utilization"
Severity is not "Clear"
------------------

SignalFX_PaloAlto_EC2_CPUUtilization
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
Type contains "CRBG_PaloAlto_CPU_Utilization"
Severity is not "Clear"
------------------

SignalFX_EC2_Status_Check
action: "Yes"
enabled: "True"
#logic: "AND"
--------Fields----------
source is "Splunk Observability Cloud"
------------------

SignalFX_PCS_ECS_CPUUTILIZATION
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source is "Splunk Observability Cloud"
Type contains "CRBG_ECS_CPU_Utilization"
Severity is not "Clear"
------------------

SignalFX_PCS_ECS_Docker_HeartBeat
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
Type contains "CRBG_ECS_Docker"
Severity is not "Clear"
------------------

SignalFX_Memory_Utilization
action: "Yes"
enabled: "True"
#logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
------------------

SignalFX_SwapmemoryUtilization
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
Type is "PCS_CRBG_EC2_Prod_Linux_SwapmemoryUtilization"
Severity is not "Clear"
------------------

SignalFX_All_Events_Generic
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
Severity is not "Clear"
Type contains ""PCS_CRBG_EC2"
------------------

SignalFX_Azure_Generic
action: "Yes"
enabled: "True"
logic: "AND"
--------Fields----------
source is "Splunk Observability Cloud"
messageBody contains "azure"
Severity is not "Clear"
------------------

SignalFX_Filter_All
action: "No"
enabled: "True"
#logic: "AND"
--------Fields----------
source contains "Splunk Observability Cloud"
------------------
